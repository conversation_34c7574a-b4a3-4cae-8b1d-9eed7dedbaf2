# 混凝土裂缝检测算法系统

这是一个综合的混凝土裂缝检测系统，集成了传统算法、深度学习算法和Transformer模型，提供完整的从图像输入到结果输出的检测流程。

## 🚀 系统特性

### 算法覆盖
- **传统算法**: Otsu阈值分割、K-means聚类、Skele-Marker去噪
- **深度学习**: ResNet分类、YOLO检测、U-Net分割
- **Transformer**: Vision Transformer、PCTNet分割网络
- **像素标定**: 相机标定、尺寸量化

### 功能特性
- 🖥️ **图形界面**: 用户友好的GUI界面
- 📊 **性能对比**: 多算法结果对比分析
- 📏 **尺寸量化**: 像素到物理尺寸的转换
- 📁 **批量处理**: 支持多图像批量检测
- 📋 **报告生成**: 详细的检测报告和可视化

## 📁 项目结构

```
ultralytics-main-max-area-R/
├── traditional_algorithms.py          # 传统算法模块
├── deep_learning_algorithms.py        # 深度学习算法模块
├── transformer_models.py              # Transformer模型模块
├── pixel_calibration.py               # 像素标定模块
├── algorithm_selection_gui.py         # GUI界面
├── comprehensive_crack_detection_system.py  # 综合检测系统
├── quick_start.py                     # 快速启动脚本
├── requirements_project.txt           # 项目依赖
├── ALGORITHM_SYSTEM_README.md         # 本文档
├── test_images/                       # 测试图像目录
└── output/                           # 输出结果目录
    ├── traditional/                   # 传统算法结果
    ├── deep_learning/                # 深度学习结果
    ├── transformer/                  # Transformer结果
    ├── comparison/                   # 算法对比图
    └── reports/                      # 检测报告
```

## 🛠️ 安装和配置

### 环境要求
- Python 3.9+
- CUDA 11.2+ (可选，用于GPU加速)

### 依赖安装

1. **激活conda环境**:
```bash
conda activate ultralytics-main-max-area-R
```

2. **安装核心依赖**:
```bash
# 深度学习框架
pip install torch torchvision torchaudio --index-url https://download.pytorch.org/whl/cu121

# 计算机视觉和科学计算
pip install opencv-python numpy scipy pandas scikit-learn scikit-image matplotlib

# YOLO和GUI
pip install ultralytics PyQt5

# 其他工具
pip install tqdm pyyaml requests psutil
```

3. **或使用requirements文件**:
```bash
pip install -r requirements_project.txt
```

### 验证安装
```bash
python quick_start.py --check
```

## 🚀 快速开始

### 1. GUI界面（推荐）
```bash
python quick_start.py --gui
```

### 2. 命令行使用

**检测单个图像**:
```bash
python quick_start.py --image test_images/sample_crack.jpg
```

**批量检测**:
```bash
python quick_start.py --batch test_images/
```

**指定算法**:
```bash
python quick_start.py --image test.jpg --algorithm traditional
python quick_start.py --image test.jpg --algorithm deep_learning
python quick_start.py --image test.jpg --algorithm transformer
```

### 3. 运行演示
```bash
python quick_start.py --demo
```

## 📖 算法详解

### 传统算法 (traditional_algorithms.py)

#### 1. Otsu阈值分割
- **原理**: 自动计算最优阈值，最大化类间方差
- **适用**: 对比度较好的裂缝图像
- **优点**: 计算快速，无需训练
- **缺点**: 对噪声敏感

#### 2. K-means聚类
- **原理**: 将像素按灰度值聚类为裂缝和背景
- **适用**: 灰度分布明显的图像
- **优点**: 自适应性强
- **缺点**: 对初始值敏感

#### 3. Skele-Marker去噪
- **原理**: 基于连通域面积和骨架长度的双重筛选
- **适用**: 噪声较多的二值化图像
- **优点**: 去噪效果好
- **缺点**: 可能过度去噪

### 深度学习算法 (deep_learning_algorithms.py)

#### 1. 图像分类 (ResNet)
- **原理**: 滑动窗口 + ResNet分类
- **输入**: 图像块 (224×224)
- **输出**: 裂缝/非裂缝概率
- **优点**: 精度高，泛化能力强
- **缺点**: 计算量大

#### 2. 目标检测 (YOLO)
- **原理**: 端到端的目标检测网络
- **输入**: 完整图像
- **输出**: 边界框 + 置信度
- **优点**: 速度快，实时性好
- **缺点**: 对小目标检测困难

#### 3. 语义分割 (U-Net)
- **原理**: 编码器-解码器结构
- **输入**: 完整图像
- **输出**: 像素级掩码
- **优点**: 精确的边界定位
- **缺点**: 内存需求大

### Transformer算法 (transformer_models.py)

#### 1. Vision Transformer (ViT)
- **原理**: 图像分块 + 自注意力机制
- **特点**: 全局特征学习
- **优点**: 捕捉长距离依赖
- **缺点**: 需要大量训练数据

#### 2. PCTNet
- **原理**: CNN + Transformer混合架构
- **特点**: 局部和全局特征融合
- **优点**: 平衡精度和效率
- **缺点**: 模型复杂度高

### 像素标定 (pixel_calibration.py)

#### 1. 相机标定
- **方法**: 棋盘格标定法
- **输出**: 内参矩阵、畸变系数
- **公式**: `s = (d_w × l) / (P × f)`

#### 2. 尺寸计算
- **长度**: Freeman编码 + 骨架提取
- **宽度**: 法线距离法 / 距离变换法
- **面积**: 像素计数 × 像素面积

## 🎯 使用指南

### GUI界面使用

1. **加载图像**: 点击"加载图像"选择待检测图像
2. **选择算法**: 
   - 算法类型: 传统算法/深度学习/Transformer
   - 检测方法: 根据算法类型选择具体方法
3. **参数配置**: 调整算法参数
4. **像素标定**: 设置像素比例
5. **开始检测**: 点击"开始检测"
6. **查看结果**: 在"检测结果"标签页查看结果
7. **导出结果**: 保存结果图像和检测报告

### 命令行使用

#### 综合检测系统
```bash
python comprehensive_crack_detection_system.py \
    --input test_images/crack.jpg \
    --output results/ \
    --algorithms traditional deep_learning \
    --pixel-scale 0.1
```

#### 单独算法模块
```bash
# 传统算法
python traditional_algorithms.py

# 深度学习算法
python deep_learning_algorithms.py

# Transformer算法
python transformer_models.py

# 像素标定
python pixel_calibration.py
```

## 📊 结果分析

### 输出文件说明

1. **检测结果图像**: 
   - `*_result.jpg`: 可视化检测结果
   - `*_comparison.png`: 多算法对比图

2. **检测报告**:
   - `*_report.md`: Markdown格式报告
   - `*_results.json`: JSON格式详细数据

3. **调试图像**:
   - `*_debug/`: 中间处理步骤图像

### 性能指标

- **处理时间**: 各算法的执行时间
- **检测精度**: 裂缝识别准确率
- **物理尺寸**: 长度、宽度、面积测量
- **形态特征**: 长宽比、圆形度、粗糙度等

## 🔧 高级配置

### 算法参数调优

#### 传统算法
```python
# Otsu阈值
params = {
    'enable_denoising': True,
    'enable_morphology': True
}

# K-means聚类
params = {
    'k': 2,  # 聚类数量
    'enable_denoising': True
}
```

#### 深度学习
```python
# YOLO检测
params = {
    'confidence_threshold': 0.5,
    'iou_threshold': 0.45
}

# U-Net分割
params = {
    'threshold': 0.5
}
```

#### Transformer
```python
# ViT分类
params = {
    'patch_size': 224,
    'stride': 112,
    'confidence_threshold': 0.5
}
```

### 像素标定配置
```python
# 手动设置
calculator.set_pixel_scale_manual(0.1)  # mm/pixel

# 相机标定
calculator.calculate_pixel_scale(
    object_distance=1000,  # mm
    sensor_width=23.6,     # mm
    image_width=1920,      # pixels
    focal_length=50        # mm
)
```

## 🐛 故障排除

### 常见问题

1. **CUDA内存不足**:
   - 减小batch size
   - 使用CPU模式: `device='cpu'`

2. **模型加载失败**:
   - 检查模型文件路径
   - 确认模型格式正确

3. **GUI启动失败**:
   - 安装PyQt5: `pip install PyQt5`
   - 检查显示环境

4. **依赖包缺失**:
   - 运行: `python quick_start.py --check`
   - 按提示安装缺失包

### 性能优化

1. **GPU加速**: 确保CUDA环境正确配置
2. **内存优化**: 处理大图像时分块处理
3. **并行处理**: 批量检测时使用多进程

## 📈 扩展开发

### 添加新算法

1. **创建算法类**:
```python
class NewAlgorithm:
    def __init__(self):
        pass
    
    def detect(self, image):
        # 实现检测逻辑
        return result
```

2. **集成到系统**:
```python
# 在comprehensive_crack_detection_system.py中添加
def _run_new_algorithm(self, image):
    # 调用新算法
    pass
```

3. **更新GUI界面**:
```python
# 在algorithm_selection_gui.py中添加选项
self.method_combo.addItems(["新算法"])
```

### 自定义评估指标

```python
def custom_evaluation(ground_truth, prediction):
    # 实现自定义评估逻辑
    precision = calculate_precision(ground_truth, prediction)
    recall = calculate_recall(ground_truth, prediction)
    f1_score = 2 * precision * recall / (precision + recall)
    return {'precision': precision, 'recall': recall, 'f1': f1_score}
```

## 📚 参考文献

1. Otsu, N. (1979). A threshold selection method from gray-level histograms.
2. Ronneberger, O., et al. (2015). U-Net: Convolutional Networks for Biomedical Image Segmentation.
3. Dosovitskiy, A., et al. (2020). An Image is Worth 16x16 Words: Transformers for Image Recognition at Scale.
4. Redmon, J., et al. (2016). You Only Look Once: Unified, Real-Time Object Detection.

## 📄 许可证

本项目基于MIT许可证开源。

## 🤝 贡献

欢迎提交Issue和Pull Request来改进本项目。

---

**联系方式**: 如有问题请提交Issue或联系开发团队。
