#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
混凝土裂缝宽度识别算法
Concrete Crack Width Detection Algorithm

实现多种裂缝检测和宽度测量算法：
1. 传统图像处理算法（Otsu阈值、K-means聚类）
2. 形态学处理和骨架提取
3. 裂缝宽度测量（距离变换法、法线距离法）
4. 像素标定和物理尺寸转换
5. 结果可视化和报告生成

作者: AI Assistant
日期: 2025-01-14
"""

import cv2
import numpy as np
import matplotlib.pyplot as plt
import matplotlib.patches as patches
from matplotlib.patches import Rectangle
from skimage import morphology, measure, filters
from skimage.morphology import skeletonize, disk, binary_erosion, binary_dilation
from skimage.measure import label, regionprops
from scipy import ndimage
from scipy.spatial.distance import cdist
import json
import os
from datetime import datetime
from typing import Tuple, List, Dict, Optional
import warnings
warnings.filterwarnings('ignore')

class CrackWidthDetector:
    """混凝土裂缝宽度检测器"""
    
    def __init__(self, pixel_scale: float = 0.1):
        """
        初始化检测器
        
        Args:
            pixel_scale: 像素比例 (mm/pixel)，默认0.1mm/pixel
        """
        self.pixel_scale = pixel_scale  # mm/pixel
        self.results = {}
        self.debug_images = {}
        
    def set_pixel_scale(self, pixel_scale: float):
        """设置像素比例"""
        self.pixel_scale = pixel_scale
        
    def calculate_pixel_scale_from_camera(self, 
                                        object_distance: float,
                                        sensor_width: float = 23.6,
                                        image_width: int = 1920,
                                        focal_length: float = 50) -> float:
        """
        基于相机参数计算像素比例
        
        Args:
            object_distance: 物距 (mm)
            sensor_width: 传感器宽度 (mm)
            image_width: 图像宽度 (pixels)
            focal_length: 焦距 (mm)
            
        Returns:
            像素比例 (mm/pixel)
        """
        # 公式: s = (d_w × l) / (P × f)
        pixel_scale = (object_distance * sensor_width) / (image_width * focal_length)
        self.pixel_scale = pixel_scale
        return pixel_scale
    
    def preprocess_image(self, image: np.ndarray) -> np.ndarray:
        """
        图像预处理
        
        Args:
            image: 输入图像
            
        Returns:
            预处理后的灰度图像
        """
        # 转换为灰度图
        if len(image.shape) == 3:
            gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)
        else:
            gray = image.copy()
            
        # 高斯滤波去噪
        gray = cv2.GaussianBlur(gray, (5, 5), 0)
        
        # 直方图均衡化增强对比度
        gray = cv2.equalizeHist(gray)
        
        self.debug_images['preprocessed'] = gray
        return gray
    
    def otsu_threshold(self, image: np.ndarray) -> Tuple[np.ndarray, int]:
        """
        Otsu阈值分割
        
        Args:
            image: 灰度图像
            
        Returns:
            二值化图像和阈值
        """
        # 计算Otsu阈值
        threshold, binary = cv2.threshold(image, 0, 255, cv2.THRESH_BINARY_INV + cv2.THRESH_OTSU)
        
        # 形态学处理去噪
        kernel = cv2.getStructuringElement(cv2.MORPH_ELLIPSE, (3, 3))
        binary = cv2.morphologyEx(binary, cv2.MORPH_OPEN, kernel)
        binary = cv2.morphologyEx(binary, cv2.MORPH_CLOSE, kernel)
        
        self.debug_images['otsu_binary'] = binary
        return binary, int(threshold)
    
    def kmeans_clustering(self, image: np.ndarray, k: int = 2) -> np.ndarray:
        """
        K-means聚类分割
        
        Args:
            image: 灰度图像
            k: 聚类数量
            
        Returns:
            二值化图像
        """
        # 重塑图像数据
        data = image.reshape((-1, 1)).astype(np.float32)
        
        # K-means聚类
        criteria = (cv2.TERM_CRITERIA_EPS + cv2.TERM_CRITERIA_MAX_ITER, 20, 1.0)
        _, labels, centers = cv2.kmeans(data, k, None, criteria, 10, cv2.KMEANS_RANDOM_CENTERS)
        
        # 选择最暗的聚类作为裂缝
        dark_cluster = np.argmin(centers)
        
        # 生成二值化图像
        binary = (labels.reshape(image.shape) == dark_cluster).astype(np.uint8) * 255
        
        # 形态学处理
        kernel = cv2.getStructuringElement(cv2.MORPH_ELLIPSE, (3, 3))
        binary = cv2.morphologyEx(binary, cv2.MORPH_OPEN, kernel)
        
        self.debug_images['kmeans_binary'] = binary
        return binary
    
    def remove_noise_by_area(self, binary: np.ndarray, min_area: int = 50) -> np.ndarray:
        """
        基于连通域面积去除噪声
        
        Args:
            binary: 二值化图像
            min_area: 最小连通域面积
            
        Returns:
            去噪后的二值化图像
        """
        # 连通域分析
        num_labels, labels, stats, centroids = cv2.connectedComponentsWithStats(binary, connectivity=8)
        
        # 创建输出图像
        output = np.zeros_like(binary)
        
        # 保留面积大于阈值的连通域
        for i in range(1, num_labels):  # 跳过背景(标签0)
            area = stats[i, cv2.CC_STAT_AREA]
            if area >= min_area:
                output[labels == i] = 255
                
        self.debug_images['denoised'] = output
        return output
    
    def extract_skeleton(self, binary: np.ndarray) -> np.ndarray:
        """
        提取骨架
        
        Args:
            binary: 二值化图像
            
        Returns:
            骨架图像
        """
        # 转换为布尔类型
        binary_bool = binary > 0
        
        # 骨架提取
        skeleton = skeletonize(binary_bool)
        
        # 转换回uint8
        skeleton_img = (skeleton * 255).astype(np.uint8)
        
        self.debug_images['skeleton'] = skeleton_img
        return skeleton_img

    def measure_crack_width_distance_transform(self, binary: np.ndarray, skeleton: np.ndarray) -> Dict:
        """
        使用距离变换法测量裂缝宽度

        Args:
            binary: 二值化图像
            skeleton: 骨架图像

        Returns:
            包含宽度测量结果的字典
        """
        # 计算距离变换
        dist_transform = cv2.distanceTransform(binary, cv2.DIST_L2, 5)

        # 在骨架点处获取距离值
        skeleton_points = np.where(skeleton > 0)

        if len(skeleton_points[0]) == 0:
            return {'widths_pixel': [], 'widths_mm': [], 'mean_width_pixel': 0, 'mean_width_mm': 0}

        # 获取骨架点的距离值（半宽度）
        half_widths = dist_transform[skeleton_points]

        # 计算完整宽度（距离变换给出的是到边界的距离，即半宽度）
        widths_pixel = half_widths * 2

        # 转换为物理尺寸
        widths_mm = widths_pixel * self.pixel_scale

        # 过滤异常值（宽度为0或过大的值）
        valid_mask = (widths_pixel > 0) & (widths_pixel < 100)  # 假设最大宽度不超过100像素
        widths_pixel = widths_pixel[valid_mask]
        widths_mm = widths_mm[valid_mask]

        results = {
            'widths_pixel': widths_pixel.tolist(),
            'widths_mm': widths_mm.tolist(),
            'mean_width_pixel': float(np.mean(widths_pixel)) if len(widths_pixel) > 0 else 0,
            'mean_width_mm': float(np.mean(widths_mm)) if len(widths_mm) > 0 else 0,
            'max_width_pixel': float(np.max(widths_pixel)) if len(widths_pixel) > 0 else 0,
            'max_width_mm': float(np.max(widths_mm)) if len(widths_mm) > 0 else 0,
            'min_width_pixel': float(np.min(widths_pixel)) if len(widths_pixel) > 0 else 0,
            'min_width_mm': float(np.min(widths_mm)) if len(widths_mm) > 0 else 0
        }

        self.debug_images['distance_transform'] = (dist_transform * 255 / np.max(dist_transform)).astype(np.uint8)
        return results

    def measure_crack_width_normal_method(self, binary: np.ndarray, skeleton: np.ndarray) -> Dict:
        """
        使用法线距离法测量裂缝宽度

        Args:
            binary: 二值化图像
            skeleton: 骨架图像

        Returns:
            包含宽度测量结果的字典
        """
        # 获取骨架点
        skeleton_points = np.where(skeleton > 0)

        if len(skeleton_points[0]) == 0:
            return {'widths_pixel': [], 'widths_mm': [], 'mean_width_pixel': 0, 'mean_width_mm': 0}

        widths_pixel = []

        # 对每个骨架点计算法线方向的宽度
        for i in range(len(skeleton_points[0])):
            y, x = skeleton_points[0][i], skeleton_points[1][i]

            # 计算局部梯度方向（法线方向）
            if y > 0 and y < binary.shape[0]-1 and x > 0 and x < binary.shape[1]-1:
                # 使用Sobel算子计算梯度
                gx = float(binary[y, x+1]) - float(binary[y, x-1])
                gy = float(binary[y+1, x]) - float(binary[y-1, x])

                # 法线方向（垂直于梯度方向）
                if gx != 0 or gy != 0:
                    norm = np.sqrt(gx*gx + gy*gy)
                    nx, ny = -gy/norm, gx/norm  # 法线方向单位向量

                    # 沿法线方向搜索边界
                    width = self._measure_width_along_normal(binary, x, y, nx, ny)
                    if width > 0:
                        widths_pixel.append(width)

        widths_pixel = np.array(widths_pixel)
        widths_mm = widths_pixel * self.pixel_scale

        # 过滤异常值
        if len(widths_pixel) > 0:
            valid_mask = (widths_pixel > 0) & (widths_pixel < 100)
            widths_pixel = widths_pixel[valid_mask]
            widths_mm = widths_mm[valid_mask]

        results = {
            'widths_pixel': widths_pixel.tolist(),
            'widths_mm': widths_mm.tolist(),
            'mean_width_pixel': float(np.mean(widths_pixel)) if len(widths_pixel) > 0 else 0,
            'mean_width_mm': float(np.mean(widths_mm)) if len(widths_mm) > 0 else 0,
            'max_width_pixel': float(np.max(widths_pixel)) if len(widths_pixel) > 0 else 0,
            'max_width_mm': float(np.max(widths_mm)) if len(widths_mm) > 0 else 0,
            'min_width_pixel': float(np.min(widths_pixel)) if len(widths_pixel) > 0 else 0,
            'min_width_mm': float(np.min(widths_mm)) if len(widths_mm) > 0 else 0
        }

        return results

    def _measure_width_along_normal(self, binary: np.ndarray, x: int, y: int, nx: float, ny: float) -> float:
        """
        沿法线方向测量宽度

        Args:
            binary: 二值化图像
            x, y: 起始点坐标
            nx, ny: 法线方向单位向量

        Returns:
            宽度（像素）
        """
        h, w = binary.shape

        # 向两个方向搜索边界
        def find_boundary(dx, dy):
            step = 1
            while True:
                px = int(x + step * dx)
                py = int(y + step * dy)

                if px < 0 or px >= w or py < 0 or py >= h:
                    return step - 1

                if binary[py, px] == 0:  # 到达边界
                    return step - 1

                step += 1
                if step > 50:  # 防止无限循环
                    return step - 1

        # 向正负两个方向搜索
        dist1 = find_boundary(nx, ny)
        dist2 = find_boundary(-nx, -ny)

        return dist1 + dist2

    def analyze_crack_properties(self, binary: np.ndarray, skeleton: np.ndarray) -> Dict:
        """
        分析裂缝的几何特性

        Args:
            binary: 二值化图像
            skeleton: 骨架图像

        Returns:
            裂缝特性字典
        """
        # 连通域分析
        num_labels, labels, stats, centroids = cv2.connectedComponentsWithStats(binary, connectivity=8)

        crack_regions = []
        total_area_pixel = 0
        total_length_pixel = 0

        for i in range(1, num_labels):  # 跳过背景
            area = stats[i, cv2.CC_STAT_AREA]
            if area > 10:  # 过滤小区域
                # 区域属性
                region_mask = (labels == i).astype(np.uint8) * 255
                region_skeleton = cv2.bitwise_and(skeleton, skeleton, mask=region_mask)

                # 计算长度（骨架像素数）
                length = np.sum(region_skeleton > 0)

                # 计算边界框
                x, y, w, h = stats[i, cv2.CC_STAT_LEFT], stats[i, cv2.CC_STAT_TOP], stats[i, cv2.CC_STAT_WIDTH], stats[i, cv2.CC_STAT_HEIGHT]

                crack_regions.append({
                    'id': i,
                    'area_pixel': int(area),
                    'area_mm2': float(area * self.pixel_scale * self.pixel_scale),
                    'length_pixel': int(length),
                    'length_mm': float(length * self.pixel_scale),
                    'bbox': [int(x), int(y), int(w), int(h)],
                    'centroid': [float(centroids[i][0]), float(centroids[i][1])]
                })

                total_area_pixel += area
                total_length_pixel += length

        return {
            'num_cracks': len(crack_regions),
            'crack_regions': crack_regions,
            'total_area_pixel': int(total_area_pixel),
            'total_area_mm2': float(total_area_pixel * self.pixel_scale * self.pixel_scale),
            'total_length_pixel': int(total_length_pixel),
            'total_length_mm': float(total_length_pixel * self.pixel_scale)
        }

    def detect_cracks(self, image: np.ndarray, method: str = 'otsu') -> Dict:
        """
        完整的裂缝检测流程

        Args:
            image: 输入图像
            method: 检测方法 ('otsu', 'kmeans', 'combined')

        Returns:
            检测结果字典
        """
        # 预处理
        gray = self.preprocess_image(image)

        # 二值化
        if method == 'otsu':
            binary, threshold = self.otsu_threshold(gray)
        elif method == 'kmeans':
            binary = self.kmeans_clustering(gray)
            threshold = None
        elif method == 'combined':
            # 组合方法：Otsu + K-means
            binary1, threshold = self.otsu_threshold(gray)
            binary2 = self.kmeans_clustering(gray)
            binary = cv2.bitwise_or(binary1, binary2)
        else:
            raise ValueError(f"Unknown method: {method}")

        # 去噪
        binary = self.remove_noise_by_area(binary, min_area=50)

        # 骨架提取
        skeleton = self.extract_skeleton(binary)

        # 宽度测量
        width_results_dt = self.measure_crack_width_distance_transform(binary, skeleton)
        width_results_normal = self.measure_crack_width_normal_method(binary, skeleton)

        # 几何特性分析
        crack_properties = self.analyze_crack_properties(binary, skeleton)

        # 整合结果
        results = {
            'method': method,
            'threshold': threshold,
            'pixel_scale': self.pixel_scale,
            'timestamp': datetime.now().isoformat(),
            'width_distance_transform': width_results_dt,
            'width_normal_method': width_results_normal,
            'crack_properties': crack_properties,
            'images': {
                'binary': binary,
                'skeleton': skeleton,
                'original_gray': gray
            }
        }

        self.results = results
        return results

    def visualize_results(self, image: np.ndarray, results: Dict, save_path: str = None) -> np.ndarray:
        """
        可视化检测结果

        Args:
            image: 原始图像
            results: 检测结果
            save_path: 保存路径

        Returns:
            可视化图像
        """
        # 创建子图
        fig, axes = plt.subplots(2, 3, figsize=(18, 12))
        fig.suptitle(f'混凝土裂缝宽度检测结果 - {results["method"].upper()}方法', fontsize=16, fontweight='bold')

        # 原始图像
        if len(image.shape) == 3:
            axes[0, 0].imshow(cv2.cvtColor(image, cv2.COLOR_BGR2RGB))
        else:
            axes[0, 0].imshow(image, cmap='gray')
        axes[0, 0].set_title('原始图像')
        axes[0, 0].axis('off')

        # 预处理后的图像
        axes[0, 1].imshow(results['images']['original_gray'], cmap='gray')
        axes[0, 1].set_title('预处理后图像')
        axes[0, 1].axis('off')

        # 二值化结果
        axes[0, 2].imshow(results['images']['binary'], cmap='gray')
        axes[0, 2].set_title('二值化结果')
        axes[0, 2].axis('off')

        # 骨架提取结果
        axes[1, 0].imshow(results['images']['skeleton'], cmap='gray')
        axes[1, 0].set_title('骨架提取')
        axes[1, 0].axis('off')

        # 距离变换结果
        if 'distance_transform' in self.debug_images:
            axes[1, 1].imshow(self.debug_images['distance_transform'], cmap='jet')
            axes[1, 1].set_title('距离变换')
            axes[1, 1].axis('off')

        # 检测结果叠加
        result_overlay = self._create_result_overlay(image, results)
        axes[1, 2].imshow(result_overlay)
        axes[1, 2].set_title('检测结果叠加')
        axes[1, 2].axis('off')

        # 添加统计信息
        stats_text = self._generate_stats_text(results)
        fig.text(0.02, 0.02, stats_text, fontsize=10, verticalalignment='bottom',
                bbox=dict(boxstyle='round', facecolor='wheat', alpha=0.8))

        plt.tight_layout()

        if save_path:
            plt.savefig(save_path, dpi=300, bbox_inches='tight')
            print(f"结果图像已保存到: {save_path}")

        # 转换为numpy数组返回
        fig.canvas.draw()
        result_image = np.frombuffer(fig.canvas.tostring_rgb(), dtype=np.uint8)
        result_image = result_image.reshape(fig.canvas.get_width_height()[::-1] + (3,))

        plt.close(fig)
        return result_image

    def _create_result_overlay(self, image: np.ndarray, results: Dict) -> np.ndarray:
        """创建结果叠加图像"""
        if len(image.shape) == 3:
            overlay = image.copy()
        else:
            overlay = cv2.cvtColor(image, cv2.COLOR_GRAY2BGR)

        binary = results['images']['binary']
        skeleton = results['images']['skeleton']

        # 在原图上叠加裂缝区域（红色）
        crack_mask = binary > 0
        overlay[crack_mask] = [0, 0, 255]  # 红色

        # 叠加骨架（绿色）
        skeleton_mask = skeleton > 0
        overlay[skeleton_mask] = [0, 255, 0]  # 绿色

        # 添加边界框
        for crack in results['crack_properties']['crack_regions']:
            x, y, w, h = crack['bbox']
            cv2.rectangle(overlay, (x, y), (x+w, y+h), (255, 0, 0), 2)

            # 添加标签
            label = f"ID:{crack['id']} W:{crack['length_mm']:.1f}mm"
            cv2.putText(overlay, label, (x, y-10), cv2.FONT_HERSHEY_SIMPLEX, 0.5, (255, 255, 0), 1)

        return overlay

    def _generate_stats_text(self, results: Dict) -> str:
        """生成统计信息文本"""
        dt_results = results['width_distance_transform']
        normal_results = results['width_normal_method']
        props = results['crack_properties']

        stats_text = f"""检测统计信息:
方法: {results['method'].upper()}
像素比例: {results['pixel_scale']:.3f} mm/pixel
检测到裂缝数量: {props['num_cracks']}
总面积: {props['total_area_mm2']:.2f} mm²
总长度: {props['total_length_mm']:.2f} mm

宽度测量 (距离变换法):
平均宽度: {dt_results['mean_width_mm']:.3f} mm
最大宽度: {dt_results['max_width_mm']:.3f} mm
最小宽度: {dt_results['min_width_mm']:.3f} mm

宽度测量 (法线距离法):
平均宽度: {normal_results['mean_width_mm']:.3f} mm
最大宽度: {normal_results['max_width_mm']:.3f} mm
最小宽度: {normal_results['min_width_mm']:.3f} mm"""

        return stats_text

    def save_results(self, results: Dict, output_dir: str, image_name: str):
        """
        保存检测结果

        Args:
            results: 检测结果
            output_dir: 输出目录
            image_name: 图像名称
        """
        os.makedirs(output_dir, exist_ok=True)

        # 保存JSON结果
        json_path = os.path.join(output_dir, f"{image_name}_results.json")

        # 准备可序列化的结果
        serializable_results = results.copy()
        # 移除numpy数组（图像数据）
        if 'images' in serializable_results:
            del serializable_results['images']

        with open(json_path, 'w', encoding='utf-8') as f:
            json.dump(serializable_results, f, indent=2, ensure_ascii=False)

        # 保存各种处理步骤的图像
        cv2.imwrite(os.path.join(output_dir, f"{image_name}_binary.jpg"), results['images']['binary'])
        cv2.imwrite(os.path.join(output_dir, f"{image_name}_skeleton.jpg"), results['images']['skeleton'])
        cv2.imwrite(os.path.join(output_dir, f"{image_name}_preprocessed.jpg"), results['images']['original_gray'])

        # 保存调试图像
        for name, img in self.debug_images.items():
            cv2.imwrite(os.path.join(output_dir, f"{image_name}_{name}.jpg"), img)

        print(f"结果已保存到目录: {output_dir}")

    def generate_report(self, results: Dict, image_name: str, output_path: str):
        """
        生成检测报告

        Args:
            results: 检测结果
            image_name: 图像名称
            output_path: 报告保存路径
        """
        report_content = f"""# 混凝土裂缝宽度检测报告

## 基本信息
- **图像名称**: {image_name}
- **检测方法**: {results['method'].upper()}
- **检测时间**: {results['timestamp']}
- **像素比例**: {results['pixel_scale']:.3f} mm/pixel

## 检测结果概览
- **检测到裂缝数量**: {results['crack_properties']['num_cracks']}
- **总面积**: {results['crack_properties']['total_area_mm2']:.2f} mm²
- **总长度**: {results['crack_properties']['total_length_mm']:.2f} mm

## 宽度测量结果

### 距离变换法
- **平均宽度**: {results['width_distance_transform']['mean_width_mm']:.3f} mm
- **最大宽度**: {results['width_distance_transform']['max_width_mm']:.3f} mm
- **最小宽度**: {results['width_distance_transform']['min_width_mm']:.3f} mm
- **测量点数**: {len(results['width_distance_transform']['widths_mm'])}

### 法线距离法
- **平均宽度**: {results['width_normal_method']['mean_width_mm']:.3f} mm
- **最大宽度**: {results['width_normal_method']['max_width_mm']:.3f} mm
- **最小宽度**: {results['width_normal_method']['min_width_mm']:.3f} mm
- **测量点数**: {len(results['width_normal_method']['widths_mm'])}

## 详细裂缝信息

"""

        for i, crack in enumerate(results['crack_properties']['crack_regions']):
            report_content += f"""### 裂缝 {crack['id']}
- **面积**: {crack['area_mm2']:.2f} mm²
- **长度**: {crack['length_mm']:.2f} mm
- **边界框**: {crack['bbox']}
- **质心**: ({crack['centroid'][0]:.1f}, {crack['centroid'][1]:.1f})

"""

        report_content += f"""## 技术参数
- **阈值**: {results.get('threshold', 'N/A')}
- **最小连通域面积**: 50 像素
- **像素比例计算方法**: 手动设置

## 算法说明
本报告使用了以下算法进行裂缝检测和宽度测量：

1. **图像预处理**: 高斯滤波去噪 + 直方图均衡化
2. **二值化**: {results['method'].upper()}方法
3. **去噪**: 基于连通域面积的噪声去除
4. **骨架提取**: 形态学骨架化算法
5. **宽度测量**: 距离变换法 + 法线距离法

---
*报告生成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}*
"""

        with open(output_path, 'w', encoding='utf-8') as f:
            f.write(report_content)

        print(f"检测报告已保存到: {output_path}")


def main():
    """主函数 - 演示用法"""
    import argparse

    parser = argparse.ArgumentParser(description='混凝土裂缝宽度检测')
    parser.add_argument('--image', type=str, required=True, help='输入图像路径')
    parser.add_argument('--output', type=str, default='output', help='输出目录')
    parser.add_argument('--method', type=str, default='otsu', choices=['otsu', 'kmeans', 'combined'], help='检测方法')
    parser.add_argument('--pixel-scale', type=float, default=0.1, help='像素比例 (mm/pixel)')
    parser.add_argument('--show', action='store_true', help='显示结果')

    args = parser.parse_args()

    # 检查输入文件
    if not os.path.exists(args.image):
        print(f"错误: 输入图像文件不存在: {args.image}")
        return

    # 加载图像
    image = cv2.imread(args.image)
    if image is None:
        print(f"错误: 无法加载图像: {args.image}")
        return

    # 创建检测器
    detector = CrackWidthDetector(pixel_scale=args.pixel_scale)

    # 执行检测
    print(f"正在检测图像: {args.image}")
    print(f"使用方法: {args.method}")
    print(f"像素比例: {args.pixel_scale} mm/pixel")

    results = detector.detect_cracks(image, method=args.method)

    # 获取图像名称
    image_name = os.path.splitext(os.path.basename(args.image))[0]

    # 创建输出目录
    os.makedirs(args.output, exist_ok=True)

    # 可视化结果
    result_image_path = os.path.join(args.output, f"{image_name}_result.jpg")
    result_image = detector.visualize_results(image, results, save_path=result_image_path)

    # 保存结果
    detector.save_results(results, args.output, image_name)

    # 生成报告
    report_path = os.path.join(args.output, f"{image_name}_report.md")
    detector.generate_report(results, image_name, report_path)

    # 打印结果摘要
    print("\n" + "="*50)
    print("检测结果摘要:")
    print("="*50)
    print(f"检测到裂缝数量: {results['crack_properties']['num_cracks']}")
    print(f"总面积: {results['crack_properties']['total_area_mm2']:.2f} mm²")
    print(f"总长度: {results['crack_properties']['total_length_mm']:.2f} mm")
    print(f"平均宽度 (距离变换): {results['width_distance_transform']['mean_width_mm']:.3f} mm")
    print(f"平均宽度 (法线距离): {results['width_normal_method']['mean_width_mm']:.3f} mm")
    print("="*50)

    if args.show:
        plt.figure(figsize=(12, 8))
        plt.imshow(result_image)
        plt.axis('off')
        plt.title('裂缝检测结果')
        plt.show()


if __name__ == "__main__":
    main()
