#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
混凝土裂缝宽度检测测试脚本
Test Script for Concrete Crack Width Detection

用于测试和演示裂缝检测算法的功能
"""

import cv2
import numpy as np
import os
import glob
from crack_width_detection import CrackWidthDetector
import matplotlib.pyplot as plt

def test_single_image(image_path: str, output_dir: str = "output"):
    """
    测试单张图像的裂缝检测
    
    Args:
        image_path: 图像路径
        output_dir: 输出目录
    """
    print(f"\n{'='*60}")
    print(f"测试图像: {image_path}")
    print(f"{'='*60}")
    
    # 检查文件是否存在
    if not os.path.exists(image_path):
        print(f"错误: 图像文件不存在: {image_path}")
        return
    
    # 加载图像
    image = cv2.imread(image_path)
    if image is None:
        print(f"错误: 无法加载图像: {image_path}")
        return
    
    print(f"图像尺寸: {image.shape}")
    
    # 创建检测器
    detector = CrackWidthDetector(pixel_scale=0.1)  # 假设0.1mm/pixel
    
    # 获取图像名称
    image_name = os.path.splitext(os.path.basename(image_path))[0]
    
    # 创建输出目录
    os.makedirs(output_dir, exist_ok=True)
    
    # 测试不同的检测方法
    methods = ['otsu', 'kmeans', 'combined']
    
    for method in methods:
        print(f"\n--- 使用 {method.upper()} 方法检测 ---")
        
        try:
            # 执行检测
            results = detector.detect_cracks(image, method=method)
            
            # 打印结果摘要
            print(f"检测到裂缝数量: {results['crack_properties']['num_cracks']}")
            print(f"总面积: {results['crack_properties']['total_area_mm2']:.2f} mm²")
            print(f"总长度: {results['crack_properties']['total_length_mm']:.2f} mm")
            print(f"平均宽度 (距离变换): {results['width_distance_transform']['mean_width_mm']:.3f} mm")
            print(f"平均宽度 (法线距离): {results['width_normal_method']['mean_width_mm']:.3f} mm")
            
            # 创建方法特定的输出目录
            method_output_dir = os.path.join(output_dir, method)
            os.makedirs(method_output_dir, exist_ok=True)
            
            # 可视化结果
            result_image_path = os.path.join(method_output_dir, f"{image_name}_result.jpg")
            detector.visualize_results(image, results, save_path=result_image_path)
            
            # 保存结果
            detector.save_results(results, method_output_dir, image_name)
            
            # 生成报告
            report_path = os.path.join(method_output_dir, f"{image_name}_report.md")
            detector.generate_report(results, image_name, report_path)
            
            print(f"结果已保存到: {method_output_dir}")
            
        except Exception as e:
            print(f"检测过程中出现错误: {str(e)}")
            import traceback
            traceback.print_exc()

def test_batch_images(input_dir: str, output_dir: str = "batch_output"):
    """
    批量测试多张图像
    
    Args:
        input_dir: 输入图像目录
        output_dir: 输出目录
    """
    print(f"\n{'='*60}")
    print(f"批量测试目录: {input_dir}")
    print(f"{'='*60}")
    
    # 支持的图像格式
    image_extensions = ['*.jpg', '*.jpeg', '*.png', '*.bmp', '*.tiff']
    
    # 获取所有图像文件
    image_files = []
    for ext in image_extensions:
        image_files.extend(glob.glob(os.path.join(input_dir, ext)))
        image_files.extend(glob.glob(os.path.join(input_dir, ext.upper())))
    
    if not image_files:
        print(f"在目录 {input_dir} 中未找到图像文件")
        return
    
    print(f"找到 {len(image_files)} 张图像")
    
    # 创建输出目录
    os.makedirs(output_dir, exist_ok=True)
    
    # 处理每张图像
    for i, image_path in enumerate(image_files, 1):
        print(f"\n处理第 {i}/{len(image_files)} 张图像...")
        
        # 为每张图像创建子目录
        image_name = os.path.splitext(os.path.basename(image_path))[0]
        image_output_dir = os.path.join(output_dir, image_name)
        
        test_single_image(image_path, image_output_dir)

def create_synthetic_crack_image():
    """
    创建一个合成的裂缝图像用于测试
    
    Returns:
        合成的裂缝图像
    """
    # 创建背景
    height, width = 400, 600
    image = np.ones((height, width), dtype=np.uint8) * 200  # 灰色背景
    
    # 添加噪声
    noise = np.random.normal(0, 10, (height, width))
    image = np.clip(image + noise, 0, 255).astype(np.uint8)
    
    # 创建裂缝
    # 主裂缝 - 对角线
    for i in range(100, 500):
        y = int(50 + i * 0.5)
        x = i
        if 0 <= y < height and 0 <= x < width:
            # 创建不同宽度的裂缝
            width_var = 2 + int(np.sin(i * 0.02) * 2)  # 宽度变化
            for dy in range(-width_var, width_var + 1):
                for dx in range(-1, 2):
                    ny, nx = y + dy, x + dx
                    if 0 <= ny < height and 0 <= nx < width:
                        image[ny, nx] = max(0, image[ny, nx] - 100)
    
    # 分支裂缝
    for i in range(200, 400):
        y = int(150 + i * 0.3)
        x = i
        if 0 <= y < height and 0 <= x < width:
            for dy in range(-1, 2):
                for dx in range(-1, 2):
                    ny, nx = y + dy, x + dx
                    if 0 <= ny < height and 0 <= nx < width:
                        image[ny, nx] = max(0, image[ny, nx] - 80)
    
    # 垂直裂缝
    x = 300
    for y in range(50, 200):
        width_var = 1 + int(np.sin(y * 0.05))
        for dx in range(-width_var, width_var + 1):
            nx = x + dx
            if 0 <= nx < width:
                image[y, nx] = max(0, image[y, nx] - 90)
    
    return image

def demo():
    """演示功能"""
    print("混凝土裂缝宽度检测算法演示")
    print("="*50)
    
    # 创建合成测试图像
    print("1. 创建合成测试图像...")
    synthetic_image = create_synthetic_crack_image()
    
    # 保存合成图像
    os.makedirs("demo_output", exist_ok=True)
    cv2.imwrite("demo_output/synthetic_crack.jpg", synthetic_image)
    print("合成图像已保存到: demo_output/synthetic_crack.jpg")
    
    # 测试合成图像
    print("\n2. 测试合成图像...")
    test_single_image("demo_output/synthetic_crack.jpg", "demo_output")
    
    # 如果test目录存在，测试真实图像
    if os.path.exists("test") and os.path.isdir("test"):
        print("\n3. 测试真实图像...")
        test_batch_images("test", "real_test_output")
    else:
        print("\n3. 跳过真实图像测试（test目录不存在）")
    
    print("\n演示完成！")
    print("请查看输出目录中的结果图像和报告。")

if __name__ == "__main__":
    import sys
    
    if len(sys.argv) > 1:
        if sys.argv[1] == "demo":
            demo()
        elif sys.argv[1] == "batch" and len(sys.argv) > 2:
            test_batch_images(sys.argv[2])
        else:
            test_single_image(sys.argv[1])
    else:
        demo()
