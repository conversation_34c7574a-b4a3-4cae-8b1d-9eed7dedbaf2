# 项目核心依赖包
# 深度学习框架
torch>=1.8.0
torchvision>=0.9.0
torchaudio>=0.8.0

# YOLO和计算机视觉
ultralytics>=8.0.0

# 基础科学计算
numpy<2.0
scipy>=1.4.1
pandas>=1.1.4

# 图像处理
opencv-python>=4.6.0
pillow>=7.1.2
scikit-image>=0.18.0
matplotlib>=3.3.0

# 机器学习
scikit-learn>=1.0.0

# GUI框架
PyQt5>=5.15.0

# 工具包
tqdm>=4.64.0
pyyaml>=5.3.1
requests>=2.23.0
psutil

# 形态学处理已包含在scikit-image中

# 其他工具
py-cpuinfo
ultralytics-thop>=2.0.0
